#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微博识别和排序逻辑
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from config import *
import time

def test_weibo_detection():
    """测试微博检测逻辑"""
    print("=== 微博检测测试 ===")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument(f"--user-agent={USER_AGENT}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # 访问目标页面
        print(f"访问页面: {TARGET_URL}")
        driver.get(TARGET_URL)
        time.sleep(10)  # 等待页面加载
        
        print(f"页面标题: {driver.title}")
        print(f"当前URL: {driver.current_url}")
        
        # 测试微博选择器
        print("\n=== 测试微博选择器 ===")
        for i, selector in enumerate(SELECTORS["weibo_cards"]):
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"选择器 {i+1}: {selector}")
                print(f"  找到元素数量: {len(elements)}")
                
                if elements:
                    # 检查前几个元素的属性
                    for j, element in enumerate(elements[:3]):
                        try:
                            data_index = element.get_attribute('data-index')
                            style = element.get_attribute('style') or ''
                            is_visible = element.is_displayed()
                            text_length = len(element.text.strip())
                            
                            print(f"    元素 {j+1}: data-index={data_index}, visible={is_visible}, text_len={text_length}")
                            if 'translateY(-9999px)' in style:
                                print(f"      ⚠️ 隐藏元素 (translateY(-9999px))")
                        except Exception as e:
                            print(f"    元素 {j+1}: 检查失败 - {e}")
                            
            except Exception as e:
                print(f"选择器 {i+1} 失败: {e}")
        
        # 测试改进的微博检测逻辑
        print("\n=== 测试改进的微博检测逻辑 ===")
        all_weibos = []
        for selector in SELECTORS["weibo_cards"]:
            try:
                found_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                all_weibos.extend(found_elements)
            except:
                continue
        
        print(f"总共找到 {len(all_weibos)} 个微博元素")
        
        # 过滤和排序
        valid_weibos = []
        for weibo in all_weibos:
            try:
                # 检查是否隐藏
                style = weibo.get_attribute('style') or ''
                parent_style = ''
                try:
                    parent = weibo.find_element(By.XPATH, '..')
                    parent_style = parent.get_attribute('style') or ''
                except:
                    pass
                
                if ('translateY(-9999px)' in style or 'translateY(-9999px)' in parent_style or
                    'z-index: -1' in style or 'z-index: -1' in parent_style):
                    continue
                
                # 检查文本内容
                if weibo.text.strip() and weibo not in [w[1] for w in valid_weibos]:
                    data_index = weibo.get_attribute('data-index')
                    if data_index:
                        try:
                            index_num = int(data_index)
                            valid_weibos.append((index_num, weibo))
                        except:
                            valid_weibos.append((999, weibo))
                    else:
                        valid_weibos.append((999, weibo))
            except:
                continue
        
        # 排序
        valid_weibos.sort(key=lambda x: x[0])
        
        print(f"有效微博数量: {len(valid_weibos)}")
        print("微博排序（按data-index）:")
        for i, (index, weibo) in enumerate(valid_weibos[:10]):
            try:
                # 提取时间信息
                time_text = "未知时间"
                for time_selector in SELECTORS["weibo_times"]:
                    try:
                        time_element = weibo.find_element(By.CSS_SELECTOR, time_selector)
                        time_text = time_element.text.strip()
                        if time_text:
                            break
                    except:
                        continue
                
                # 提取内容片段
                content_preview = weibo.text.strip()[:100] + "..." if len(weibo.text.strip()) > 100 else weibo.text.strip()
                
                print(f"  第{i+1}条: data-index={index}, 时间={time_text}")
                print(f"    内容预览: {content_preview}")
                print()
                
            except Exception as e:
                print(f"  第{i+1}条: data-index={index}, 提取信息失败 - {e}")
        
    except Exception as e:
        print(f"测试过程出错: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    test_weibo_detection()
