#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博爬虫配置文件
"""

# 目标URL - 桌面版微博
TARGET_URL = "https://s.weibo.com/weibo?q=%23%E9%87%8D%E5%BA%86%E5%85%AC%E4%BA%A4%E5%9D%A0%E6%B1%9F%23&xsort=hot&suball=1&Refer=g&page=1"

# 分页配置
MAX_PAGES = 5  # 最大爬取页数

# 爬取配置
WEIBO_COUNT = 20  # 目标微博数量
MAX_COMMENTS_PER_WEIBO = 300  # 每条微博最大评论数
TARGET_TOTAL_COMMENTS = 5000  # 目标总评论数

# 延时配置（秒）
MIN_DELAY = 2
MAX_DELAY = 5
LOAD_MORE_DELAY = 3

# 浏览器配置 - 桌面版
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# CSS选择器配置 - 基于桌面版微博HTML结构优化
SELECTORS = {
    # 微博相关选择器 - 基于桌面版实际HTML结构
    "weibo_cards": [
        ".card-wrap",  # 主要选择器 - 桌面版
        ".card",
        "[class*='card-wrap']",
        "[class*='card']"
    ],

    # 评论按钮选择器 - 基于您提供的HTML结构
    "comment_buttons": [
        "a[action-type='feed_list_comment']",  # 主要选择器 - 桌面版
        ".woo-box-flex.woo-box-alignCenter.woo-box-justifyCenter[action-type='feed_list_comment']",
        "[action-type='feed_list_comment']",
        ".toolbar_iconWrap .woo-font--comment",
        "[class*='comment']"
    ],

    # 微博内容选择器 - 基于桌面版实际HTML结构
    "weibo_content": [
        ".txt",  # 主要选择器 - 桌面版
        ".content .txt",
        "[class*='txt']",
        ".weibo-text",
        "[class*='content']"
    ],

    # 用户昵称选择器 - 基于桌面版实际HTML结构
    "user_nicknames": [
        ".name",  # 主要选择器 - 桌面版
        ".info .name",
        "a.name",
        "[class*='name']",
        ".user-name"
    ],

    # 微博时间选择器 - 基于桌面版实际HTML结构
    "weibo_times": [
        ".from a",  # 主要选择器 - 桌面版
        ".from",
        "[class*='from']",
        ".time",
        "[class*='time']"
    ],

    # 更多评论按钮选择器 - 基于您提供的HTML结构
    "more_comments": [
        "a:contains('后面还有')",  # 主要选择器
        "a:contains('条评论，点击查看')",
        "a[href*='weibo.com'][href*='/']",  # 微博详情页链接
        ".wbicon",
        "a:contains('评论')",
        "[href*='weibo.com']"
    ],

    # 评论列表选择器 - 支持多种页面结构
    "comment_lists": [
        # 详情页面 - Vue虚拟滚动结构
        ".vue-recycle-scroller__item-view .wbpro-scroller-item",
        ".wbpro-scroller-item",
        ".wbpro-list .item1",
        # 原始评论页面结构
        ".list-ul .comment-item",
        ".comment-list .comment-item",
        "[class*='comment-item']",
        ".WB_detail .list_ul .comment_item",
        ".comment_lists .comment_item"
    ],

    # 评论者昵称选择器 - 支持多种页面结构
    "nicknames": [
        # 详情页面结构
        ".text a[usercard]",
        "a[usercard]",
        ".con1 .text a",
        # 原始结构
        ".WB_text a:first-child",
        ".comment-info .name",
        "[class*='name']"
    ],

    # 评论内容选择器 - 支持多种页面结构
    "contents": [
        # 详情页面结构
        ".text span",
        ".con1 .text span",
        ".text",
        ".con1 .text",
        # 原始结构
        ".WB_text",
        ".comment-info .txt",
        "[class*='text']",
        ".content"
    ],

    # 评论时间选择器 - 支持多种页面结构
    "times": [
        # 详情页面结构
        ".info div:first-child",
        ".info",
        # 原始结构
        ".WB_from a",
        ".comment-info .time",
        "[class*='time']",
        ".from"
    ],

    # 加载更多按钮选择器
    "load_more": [
        ".WB_cardmore",
        ".more_txt",
        "[class*='more']",
        "a[action-type='feed_list_page_morelist']"
    ]
}

# 输出文件配置
OUTPUT_FILES = {
    "csv": "weibo_comments.csv",
    "json": "weibo_comments.json"
}

# CSV文件列名
CSV_COLUMNS = ["微博内容", "评论者昵称", "评论内容", "评论时间", "爬取时间"]
