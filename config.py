#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博爬虫配置文件
"""

# 目标URL - 用户指定的微博页面
TARGET_URL = "https://weibo.com/u/1644114654?tabtype=feed&key_word=%E9%87%8D%E5%BA%86%E4%B8%87%E5%B7%9E%E5%85%AC%E4%BA%A4&end_time=1541001600"

# 分页配置
MAX_PAGES = 1  # 只爬取一页，因为是特定用户页面

# 爬取配置
WEIBO_COUNT = 20  # 目标微博数量（最多不超过20条）
MAX_COMMENTS_PER_WEIBO = 300  # 每条微博最大评论数
TARGET_TOTAL_COMMENTS = 6000  # 目标总评论数（20条微博 × 300条评论）

# 延时配置（秒）
MIN_DELAY = 2
MAX_DELAY = 5
LOAD_MORE_DELAY = 3

# 浏览器配置 - 桌面版
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# CSS选择器配置 - 基于net.md和hit.md的HTML结构优化
SELECTORS = {
    # 微博相关选择器 - 基于net.md的HTML结构
    "weibo_cards": [
        ".wbpro-scroller-item",  # 主要选择器 - 基于net.md，直接选择微博项
        ".vue-recycle-scroller__item-view .wbpro-scroller-item",
        "article.Feed_wrap_3v9LH",
        ".Feed_wrap_3v9LH",
        "[data-index]",  # 通过data-index属性选择
        "[class*='Feed_wrap']",
        "[class*='wbpro-scroller-item']"
    ],

    # 评论按钮选择器 - 基于net.md的HTML结构
    "comment_buttons": [
        ".toolbar_item_1ky_D .woo-font--comment",  # 主要选择器 - 基于net.md
        ".woo-font--comment",
        ".toolbar_commentIcon_3o7HB",
        "[title='评论']",
        ".toolbar_wrap_np6Ug .woo-font--comment",
        "[class*='toolbar_item'] .woo-font--comment"
    ],

    # 微博内容选择器 - 基于net.md的HTML结构
    "weibo_content": [
        ".detail_wbtext_4CRf9",  # 主要选择器 - 基于net.md
        ".wbpro-feed-content .detail_wbtext_4CRf9",
        ".detail_text_1U10O .detail_wbtext_4CRf9",
        "[class*='detail_wbtext']",
        ".wbpro-feed-content",
        "[class*='wbpro-feed']"
    ],

    # 用户昵称选择器 - 基于net.md的HTML结构
    "user_nicknames": [
        ".head_name_24eEB span",  # 主要选择器 - 基于net.md
        ".head_name_24eEB",
        "[class*='head_name']",
        ".head_nick_1yix2 a span",
        "[class*='head_nick'] span",
        "[usercard] span"
    ],

    # 微博时间选择器 - 基于net.md的HTML结构
    "weibo_times": [
        ".head-info_time_6sFQg",  # 主要选择器 - 基于net.md
        "[class*='head-info_time']",
        ".head-info_info_2AspQ a",
        "[class*='head-info'] a",
        "[title*='2018']"
    ],

    # 更多评论按钮选择器 - 基于hit.md的HTML结构
    "more_comments": [
        "a[href*='#comment']",  # 主要选择器 - 基于hit.md
        ".RepostCommentFeed_more_idG8i",
        "a:contains('查看全部')",
        "a:contains('条评论')",
        "[href*='#comment']",
        "a[href*='weibo.com'][href*='/']"
    ],

    # 评论列表选择器 - 基于hit.md的HTML结构
    "comment_lists": [
        # 详情页面 - Vue虚拟滚动结构 - 基于hit.md
        ".vue-recycle-scroller__item-view .wbpro-scroller-item .wbpro-list",
        ".wbpro-scroller-item .wbpro-list",
        ".wbpro-list",
        ".wbpro-list .item1",
        # 备用选择器
        ".vue-recycle-scroller__item-view .wbpro-scroller-item",
        ".wbpro-scroller-item",
        "[class*='wbpro-list']"
    ],

    # 评论者昵称选择器 - 基于hit.md的HTML结构
    "nicknames": [
        # 详情页面结构 - 基于hit.md
        ".con1 .text a[usercard]",
        ".text a[usercard]",
        "a[usercard]",
        ".item1in .con1 .text a",
        "[usercard]",
        ".con1 .text a"
    ],

    # 评论内容选择器 - 基于hit.md的HTML结构
    "contents": [
        # 详情页面结构 - 基于hit.md
        ".con1 .text span",
        ".text span",
        ".con1 .text",
        ".text",
        ".item1in .con1 .text span",
        ".item1in .con1 .text"
    ],

    # 评论时间选择器 - 基于hit.md的HTML结构
    "times": [
        # 详情页面结构 - 基于hit.md
        ".info div:first-child",
        ".con1 .info div:first-child",
        ".info",
        ".con1 .info",
        ".item1in .con1 .info div"
    ],

    # 加载更多按钮选择器
    "load_more": [
        ".Scroll_nextPage_UOGEz",
        "[class*='nextPage']",
        "[class*='more']",
        ".woo-spinner-main"
    ],

    # 返回按钮选择器 - 基于hit.md的HTML结构
    "back_buttons": [
        ".Bar_back_20RmD",  # 主要选择器 - 基于hit.md
        ".woo-font--angleLeft",
        "[class*='Bar_back']",
        ".Bar_left_2J3kl",
        "[class*='back']"
    ]
}

# 输出文件配置
OUTPUT_FILES = {
    "csv": "weibo_comments.csv",
    "json": "weibo_comments.json"
}

# CSV文件列名
CSV_COLUMNS = ["微博内容", "评论者昵称", "评论内容", "评论时间", "爬取时间"]
