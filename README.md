# 微博爬虫 - 桌面版多页爬取

## 💻 项目简介

这是一个专门针对桌面版微博的爬虫程序，支持多页数据爬取，可以爬取指定话题的微博及其评论数据。

**目标网站**: https://s.weibo.com/weibo?q=%23%E9%87%8D%E5%BA%86%E5%85%AC%E4%BA%A4%E5%9D%A0%E6%B1%9F%23&xsort=hot&suball=1&Refer=g&page=1

## 📁 文件说明

- `weibo_crawler_enhanced.py` - 主爬虫程序
- `config.py` - 配置文件（URL、选择器、参数等）
- `requirements.txt` - 依赖包列表

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行爬虫
```bash
python weibo_crawler_enhanced.py
```

### 3. 程序流程
1. 自动启动Chrome浏览器（桌面版）
2. 引导用户手动登录微博
3. 登录完成后自动访问目标搜索页面
4. 自动爬取多页微博数据和评论
5. 保存数据到CSV和JSON文件

## 📊 爬取配置

可在 `config.py` 中修改以下参数：

- `TARGET_URL` - 目标搜索页面URL
- `MAX_PAGES` - 最大爬取页数（默认5页）
- `WEIBO_COUNT` - 每页目标微博数量（默认20条）
- `MAX_COMMENTS_PER_WEIBO` - 每条微博最大评论数（默认300条）
- `TARGET_TOTAL_COMMENTS` - 目标总评论数（默认5000条）
- `MIN_DELAY` / `MAX_DELAY` - 延时配置（默认2-5秒）

## 🔧 核心特性

### 多页爬取
- 支持自动翻页爬取多页数据
- 可通过调整URL中的`page`参数实现分页
- 智能停止机制，达到目标数量自动停止

### 桌面版适配
- 完全适配桌面版微博页面结构
- 支持手动登录流程
- 智能识别页面元素

### 评论按钮识别
基于您提供的HTML结构：
```html
<a href="javascript:void(0);" action-type="feed_list_comment" class="woo-box-flex woo-box-alignCenter woo-box-justifyCenter">
    <span class="woo-box-flex woo-box-alignCenter woo-box-justifyCenter toolbar_iconWrap">
        <i class="woo-font woo-font--comment toolbar_icon"></i>
    </span> 152
</a>
```

### 更多评论识别与新标签页处理
基于您提供的HTML结构：
```html
<a href="//weibo.com/5677774627/H1qk6FyUu">后面还有143条评论，点击查看 <i class="wbicon">a</i></a>
```

**新增功能**：
- 自动识别"更多评论"链接
- 在新标签页中打开完整评论页面
- 爬取完成后自动关闭新标签页
- 返回原搜索页面继续处理下一条微博

## 📄 输出文件

- `weibo_comments.csv` - CSV格式数据
- `weibo_comments.json` - JSON格式数据

包含字段：微博内容、评论者昵称、评论内容、评论时间、爬取时间

## ⚠️ 注意事项

1. **登录要求**: 桌面版微博需要登录才能访问搜索结果
2. **手动登录**: 程序会自动打开登录页面，需要用户手动完成登录
3. **浏览器要求**: 确保已安装Chrome浏览器
4. **运行时间**: 程序运行时请勿关闭浏览器窗口
5. **合规使用**: 遵守网站使用条款，合理控制爬取频率
6. **数据用途**: 仅用于学习研究目的

## 🔧 技术特点

- **桌面版适配** - 完全适配桌面版微博页面
- **多页爬取** - 支持自动翻页爬取
- **智能识别** - 自动识别页面元素
- **新标签页处理** - 自动在新标签页中获取更多评论
- **标签页管理** - 智能切换和关闭标签页
- **页面导航** - 自动返回原搜索页面
- **数据清理** - 自动清理和格式化数据
- **错误处理** - 完善的异常处理机制
- **登录验证** - 智能登录状态检查

## 📱 选择器配置

基于桌面版微博实际HTML结构优化：

- **微博卡片**: `.card-wrap`, `.card`
- **评论按钮**: `a[action-type='feed_list_comment']`
- **微博内容**: `.txt`, `.content .txt`
- **用户昵称**: `.name`, `.info .name`
- **时间信息**: `.from a`, `.from`
- **更多评论**: `a:contains('后面还有')`, `a:contains('条评论，点击查看')`

## 🎯 使用流程

1. **安装依赖**: `pip install -r requirements.txt`
2. **运行程序**: `python weibo_crawler_enhanced.py`
3. **手动登录**: 在弹出的浏览器中登录微博账号
4. **确认登录**: 登录完成后按回车键继续
5. **自动爬取**: 程序自动爬取多页数据
   - 自动点击评论按钮获取基础评论
   - 自动识别"更多评论"链接
   - 在新标签页中获取完整评论
   - 自动返回原页面继续下一条微博
6. **查看结果**: 爬取完成后查看生成的CSV和JSON文件

## 🔄 爬取流程详解

### 单条微博处理流程：
1. 在搜索页面定位微博
2. 点击评论按钮进入评论区
3. 提取当前页面的评论
4. 查找"更多评论"链接
5. 如果存在，在新标签页中打开
6. 在新标签页中提取更多评论
7. 关闭新标签页，返回搜索页面
8. 继续处理下一条微博
