#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博爬虫运行脚本
基于实际页面结构优化后的版本
"""

from weibo_crawler_enhanced import WeiboCommentCrawlerEnhanced

def main():
    """主函数"""
    print("=== 微博评论爬虫 ===")
    print("目标: 爬取新京报用户页面'重庆万州公交'相关微博评论")
    print("基于实际页面结构优化的版本")
    
    # 显示配置信息
    print(f"\n配置信息:")
    print(f"- 目标微博数: 最多20条")
    print(f"- 每条微博最大评论数: 300条")
    print(f"- 预期总评论数: 约6000条")
    print(f"- 输出文件: weibo_comments.csv, weibo_comments.json")
    
    # 确认开始
    choice = input("\n是否开始爬取? (y/n): ").lower().strip()
    if choice != 'y':
        print("已取消")
        return
    
    # 创建爬虫实例
    crawler = WeiboCommentCrawlerEnhanced()
    
    try:
        # 运行爬虫
        success = crawler.run_crawler()
        
        if success:
            print("\n🎉 爬虫任务完成！")
            print("数据文件已保存在当前目录下:")
            print("- weibo_comments.csv (CSV格式)")
            print("- weibo_comments.json (JSON格式)")
        else:
            print("\n❌ 爬虫任务失败")
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序异常: {e}")

if __name__ == "__main__":
    main()
