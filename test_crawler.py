#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博爬虫测试脚本
基于实际页面结构优化后的测试
"""

import time
from weibo_crawler_enhanced import WeiboCommentCrawlerEnhanced

def test_crawler():
    """测试爬虫功能"""
    print("=== 微博爬虫测试 ===")
    print("基于实际页面结构的优化测试")
    
    crawler = WeiboCommentCrawlerEnhanced()
    
    try:
        # 启动浏览器
        print("\n1. 启动浏览器...")
        if not crawler.setup_driver():
            print("❌ 浏览器启动失败")
            return False
        
        print("✓ 浏览器启动成功")
        
        # 手动登录
        print("\n2. 请手动登录微博...")
        if not crawler.manual_login():
            print("❌ 登录失败")
            return False
        
        print("✓ 登录成功")
        
        # 访问测试页面
        test_url = "https://weibo.com/u/1644146547?tabtype=feed&key_word=重庆万州公交"
        print(f"\n3. 访问测试页面: {test_url}")
        
        crawler.driver.get(test_url)
        time.sleep(5)
        
        print(f"页面标题: {crawler.driver.title}")
        print(f"当前URL: {crawler.driver.current_url}")
        
        # 测试微博列表获取
        print("\n4. 测试微博列表获取...")
        weibo_list = crawler.get_weibo_list(target_count=3)  # 只测试3条微博
        
        if weibo_list:
            print(f"✓ 成功获取 {len(weibo_list)} 条微博")
            
            # 测试第一条微博的评论提取
            print("\n5. 测试评论提取...")
            if len(weibo_list) > 0:
                test_weibo = weibo_list[0]
                print("测试第一条微博的评论提取...")
                
                comments = crawler.extract_comments_from_weibo(test_weibo, 0, max_comments=5)
                
                if comments:
                    print(f"✓ 成功提取 {len(comments)} 条评论")
                    
                    # 显示前3条评论
                    print("\n前3条评论预览:")
                    for i, comment in enumerate(comments[:3]):
                        print(f"\n评论 {i+1}:")
                        print(f"  昵称: {comment.get('评论者昵称', 'N/A')}")
                        print(f"  内容: {comment.get('评论内容', 'N/A')[:100]}...")
                        print(f"  时间: {comment.get('评论时间', 'N/A')}")
                else:
                    print("❌ 未能提取到评论")
            else:
                print("❌ 没有微博可供测试")
        else:
            print("❌ 未能获取微博列表")
        
        print("\n=== 测试完成 ===")
        return True
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n测试过程出错: {e}")
        return False
    finally:
        # 关闭浏览器
        if crawler.driver:
            print("正在关闭浏览器...")
            crawler.driver.quit()
            print("✓ 浏览器已关闭")

def main():
    """主函数"""
    print("微博爬虫测试脚本")
    print("此脚本将测试爬虫的基本功能")
    print("包括: 浏览器启动、登录、页面访问、微博获取、评论提取")
    
    input("\n按回车键开始测试...")
    
    success = test_crawler()
    
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
