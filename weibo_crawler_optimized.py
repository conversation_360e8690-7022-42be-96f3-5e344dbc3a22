#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博爬虫 - 基于实际HTML结构优化版本
针对截图中显示的微博页面结构进行优化
"""

import time
import csv
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from config import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weibo_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WeiboCommentCrawler:
    def __init__(self):
        """初始化爬虫"""
        self.driver = None
        self.wait = None
        self.comments_data = []
        self.processed_posts = 0
        self.total_comments = 0
        
    def setup_driver(self):
        """设置Chrome浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument(f'--user-agent={USER_AGENT}')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, WAIT_TIMEOUT)
            
            logger.info("Chrome浏览器初始化成功")
            return True
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def find_element_by_selectors(self, selectors, timeout=5):
        """使用多个选择器尝试查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                logger.debug(f"成功使用选择器找到元素: {selector}")
                return element
            except TimeoutException:
                continue
        return None
    
    def find_elements_by_selectors(self, selectors, timeout=5):
        """使用多个选择器尝试查找多个元素"""
        for selector in selectors:
            try:
                WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.debug(f"成功使用选择器找到 {len(elements)} 个元素: {selector}")
                    return elements
            except TimeoutException:
                continue
        return []
    
    def get_weibo_content(self, weibo_card):
        """提取微博内容 - 基于实际HTML结构"""
        try:
            # 尝试多个选择器获取微博内容
            content_selectors = [
                ".detail_wbtext_4CRf9",
                ".detail_text_1U10O .detail_wbtext_4CRf9", 
                ".detail_text_1U10O .detail_ogText_22108",
                "[class*='detail_wbtext']",
                ".wbpro-feed-content"
            ]
            
            for selector in content_selectors:
                try:
                    content_element = weibo_card.find_element(By.CSS_SELECTOR, selector)
                    content = content_element.text.strip()
                    if content:
                        logger.debug(f"成功提取微博内容: {content[:50]}...")
                        return content
                except NoSuchElementException:
                    continue
            
            logger.warning("未能提取到微博内容")
            return "未获取到内容"
        except Exception as e:
            logger.error(f"提取微博内容时出错: {e}")
            return "提取失败"
    
    def click_comment_button(self, weibo_card):
        """点击评论按钮 - 基于实际HTML结构"""
        try:
            # 基于截图中的结构，尝试多个评论按钮选择器
            comment_selectors = [
                ".toolbar_item_1ky_D .woo-font--comment",  # 主要选择器
                ".woo-font--comment",
                ".toolbar_iconWrap_3-r17 .woo-font--comment",
                "span.toolbar_num_JXZuI",  # 评论数字
                "[class*='toolbar_item'] .woo-font--comment",
                ".toolbar_wrap_np6Ug .woo-font--comment"
            ]
            
            for selector in comment_selectors:
                try:
                    comment_button = weibo_card.find_element(By.CSS_SELECTOR, selector)
                    if comment_button.is_displayed() and comment_button.is_enabled():
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", comment_button)
                        time.sleep(1)
                        
                        # 点击按钮
                        comment_button.click()
                        logger.info(f"成功点击评论按钮: {selector}")
                        return True
                except (NoSuchElementException, WebDriverException):
                    continue
            
            logger.warning("未找到可点击的评论按钮")
            return False
        except Exception as e:
            logger.error(f"点击评论按钮时出错: {e}")
            return False
    
    def wait_for_comment_page(self):
        """等待评论页面加载"""
        try:
            # 等待评论页面的特征元素出现
            comment_page_selectors = [
                ".vue-recycle-scroller__item-view .wbpro-scroller-item .wbpro-list",
                ".wbpro-list",
                ".Bar_back_20RmD",  # 返回按钮
                "[class*='wbpro-list']"
            ]
            
            for selector in comment_page_selectors:
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"评论页面加载完成，检测到元素: {selector}")
                    return True
                except TimeoutException:
                    continue
            
            logger.warning("评论页面加载超时")
            return False
        except Exception as e:
            logger.error(f"等待评论页面时出错: {e}")
            return False
    
    def crawl_comments_in_detail_page(self, weibo_content, max_comments=300):
        """在详情页面爬取评论 - 基于实际HTML结构"""
        comments = []
        try:
            logger.info(f"开始爬取评论，最大数量: {max_comments}")
            
            # 等待页面加载
            time.sleep(3)
            
            # 滚动加载更多评论
            last_comment_count = 0
            scroll_attempts = 0
            max_scroll_attempts = 20
            
            while len(comments) < max_comments and scroll_attempts < max_scroll_attempts:
                # 查找评论元素 - 基于实际HTML结构
                comment_elements = self.find_elements_by_selectors([
                    ".vue-recycle-scroller__item-view .wbpro-scroller-item .wbpro-list",
                    ".wbpro-scroller-item .wbpro-list", 
                    ".wbpro-list",
                    "[class*='wbpro-list']"
                ])
                
                if not comment_elements:
                    logger.warning("未找到评论元素")
                    break
                
                # 提取评论信息
                for comment_element in comment_elements[len(comments):]:
                    if len(comments) >= max_comments:
                        break
                    
                    try:
                        # 提取评论者昵称
                        nickname = self.extract_comment_nickname(comment_element)
                        
                        # 提取评论内容
                        content = self.extract_comment_content(comment_element)
                        
                        # 提取评论时间
                        comment_time = self.extract_comment_time(comment_element)
                        
                        if nickname and content:
                            comment_data = {
                                "微博内容": weibo_content,
                                "评论者昵称": nickname,
                                "评论内容": content,
                                "评论时间": comment_time,
                                "爬取时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }
                            comments.append(comment_data)
                            logger.debug(f"提取评论: {nickname} - {content[:30]}...")
                    
                    except Exception as e:
                        logger.error(f"提取单条评论时出错: {e}")
                        continue
                
                # 检查是否有新评论
                current_comment_count = len(comments)
                if current_comment_count == last_comment_count:
                    scroll_attempts += 1
                else:
                    scroll_attempts = 0
                    last_comment_count = current_comment_count
                
                # 滚动页面加载更多评论
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                logger.info(f"已提取 {len(comments)} 条评论")
            
            logger.info(f"评论爬取完成，共获取 {len(comments)} 条评论")
            return comments
            
        except Exception as e:
            logger.error(f"爬取评论时出错: {e}")
            return comments
    
    def extract_comment_nickname(self, comment_element):
        """提取评论者昵称"""
        nickname_selectors = [
            ".con1 .text a[usercard]",
            ".text a[usercard]", 
            "a[usercard]",
            ".item1in .con1 .text a",
            "[usercard]"
        ]
        
        for selector in nickname_selectors:
            try:
                nickname_element = comment_element.find_element(By.CSS_SELECTOR, selector)
                nickname = nickname_element.text.strip()
                if nickname:
                    return nickname
            except NoSuchElementException:
                continue
        return "未知用户"
    
    def extract_comment_content(self, comment_element):
        """提取评论内容"""
        content_selectors = [
            ".con1 .text span",
            ".text span",
            ".con1 .text",
            ".text",
            ".item1in .con1 .text span"
        ]
        
        for selector in content_selectors:
            try:
                content_elements = comment_element.find_elements(By.CSS_SELECTOR, selector)
                if content_elements:
                    # 合并所有文本内容
                    content_parts = []
                    for elem in content_elements:
                        text = elem.text.strip()
                        if text and text not in content_parts:
                            content_parts.append(text)
                    
                    content = " ".join(content_parts)
                    if content:
                        return content
            except NoSuchElementException:
                continue
        return "未获取到内容"
    
    def extract_comment_time(self, comment_element):
        """提取评论时间"""
        time_selectors = [
            ".info div:first-child",
            ".con1 .info div:first-child",
            ".info",
            ".con1 .info"
        ]
        
        for selector in time_selectors:
            try:
                time_element = comment_element.find_element(By.CSS_SELECTOR, selector)
                comment_time = time_element.text.strip()
                if comment_time:
                    return comment_time
            except NoSuchElementException:
                continue
        return "未知时间"

    def go_back_to_main_page(self):
        """返回主页面"""
        try:
            # 查找返回按钮
            back_selectors = [
                ".Bar_back_20RmD",
                ".woo-font--angleLeft",
                "[class*='Bar_back']",
                ".Bar_left_2J3kl"
            ]

            back_button = self.find_element_by_selectors(back_selectors)
            if back_button:
                back_button.click()
                logger.info("成功点击返回按钮")
                time.sleep(3)
                return True
            else:
                # 如果找不到返回按钮，尝试浏览器后退
                self.driver.back()
                logger.info("使用浏览器后退功能")
                time.sleep(3)
                return True
        except Exception as e:
            logger.error(f"返回主页面时出错: {e}")
            return False

    def crawl_weibo_comments(self, search_url, max_posts=20, max_comments_per_post=300):
        """爬取微博评论的主函数"""
        try:
            logger.info(f"开始爬取微博评论: {search_url}")

            # 访问搜索页面
            self.driver.get(search_url)
            time.sleep(5)

            # 等待页面加载
            weibo_cards = self.find_elements_by_selectors(SELECTORS["weibo_cards"], timeout=10)
            if not weibo_cards:
                logger.error("未找到微博卡片")
                return False

            logger.info(f"找到 {len(weibo_cards)} 个微博卡片")

            # 处理每个微博
            for i, weibo_card in enumerate(weibo_cards[:max_posts]):
                if self.processed_posts >= max_posts:
                    break

                try:
                    logger.info(f"处理第 {i+1} 个微博")

                    # 提取微博内容
                    weibo_content = self.get_weibo_content(weibo_card)

                    # 点击评论按钮
                    if self.click_comment_button(weibo_card):
                        # 等待评论页面加载
                        if self.wait_for_comment_page():
                            # 爬取评论
                            comments = self.crawl_comments_in_detail_page(
                                weibo_content, max_comments_per_post
                            )

                            # 保存评论数据
                            self.comments_data.extend(comments)
                            self.total_comments += len(comments)

                            logger.info(f"第 {i+1} 个微博爬取完成，获取 {len(comments)} 条评论")

                            # 返回主页面
                            self.go_back_to_main_page()
                        else:
                            logger.warning(f"第 {i+1} 个微博评论页面加载失败")
                    else:
                        logger.warning(f"第 {i+1} 个微博评论按钮点击失败")

                    self.processed_posts += 1

                    # 添加延迟避免被封
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"处理第 {i+1} 个微博时出错: {e}")
                    continue

            logger.info(f"爬取完成，共处理 {self.processed_posts} 个微博，获取 {self.total_comments} 条评论")
            return True

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return False

    def save_data(self):
        """保存数据到文件"""
        try:
            if not self.comments_data:
                logger.warning("没有数据需要保存")
                return

            # 保存为CSV
            csv_file = OUTPUT_FILES["csv"]
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=CSV_COLUMNS)
                writer.writeheader()
                writer.writerows(self.comments_data)
            logger.info(f"数据已保存到 {csv_file}")

            # 保存为JSON
            json_file = OUTPUT_FILES["json"]
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到 {json_file}")

        except Exception as e:
            logger.error(f"保存数据时出错: {e}")

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    # 搜索关键词
    keyword = "人工智能"

    # 构建搜索URL
    search_url = f"https://s.weibo.com/weibo?q={keyword}&page=1"

    # 创建爬虫实例
    crawler = WeiboCommentCrawler()

    try:
        # 初始化浏览器
        if not crawler.setup_driver():
            logger.error("浏览器初始化失败")
            return

        # 开始爬取
        success = crawler.crawl_weibo_comments(
            search_url=search_url,
            max_posts=20,
            max_comments_per_post=300
        )

        if success:
            # 保存数据
            crawler.save_data()
            logger.info("爬取任务完成")
        else:
            logger.error("爬取任务失败")

    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        # 关闭浏览器
        crawler.close()

if __name__ == "__main__":
    main()
