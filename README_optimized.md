# 微博爬虫优化版本

## 概述

这是基于实际微博页面HTML结构优化的爬虫版本，针对您提供的截图中显示的微博页面结构进行了专门优化。

## 主要优化点

### 1. 基于实际HTML结构的选择器

根据您提供的截图，我们识别并优化了以下关键选择器：

#### 微博卡片结构
```html
<div class="vue-recycle-scroller__item-view">
  <div class="wbpro-scroller-item">
    <div class="wbpro-feed-content">
      <!-- 微博内容 -->
    </div>
  </div>
</div>
```

#### 微博内容结构
```html
<div class="detail_text_1U10O detail_ogText_22108 wbpro-feed-ogText">
  <div class="detail_wbtext_4CRf9">
    <!-- 实际微博文本内容 -->
  </div>
</div>
```

#### 评论按钮结构
```html
<div class="toolbar_item_1ky_D toolbar_cursor_3435V">
  <div class="toolbar_iconWrap_3-r17">
    <i class="woo-font woo-font--comment toolbar_commentIcon_3o7HB"></i>
  </div>
  <span class="toolbar_num_JXZuI">118</span>
</div>
```

### 2. 多层级选择器策略

为了提高成功率，每个元素都配置了多个备选选择器：

```python
# 微博内容选择器（按优先级排序）
"weibo_content": [
    ".detail_wbtext_4CRf9",  # 主要文本内容
    ".detail_text_1U10O .detail_wbtext_4CRf9",  # 完整路径
    ".detail_text_1U10O .detail_ogText_22108",  # 原文结构
    "[class*='detail_wbtext']",  # 模糊匹配
    ".wbpro-feed-content",  # 容器级别
    "[class*='wbpro-feed']"  # 最宽泛匹配
]
```

### 3. 评论页面结构识别

基于截图中的评论页面结构：

```html
<div class="vue-recycle-scroller__item-view">
  <div class="wbpro-scroller-item">
    <div class="wbpro-list">
      <div class="item1">
        <div class="item1in">
          <div class="con1">
            <div class="text">
              <a usercard="...">用户昵称</a>
              <span>评论内容</span>
            </div>
            <div class="info">
              <div>评论时间</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

## 文件说明

### 核心文件

1. **weibo_crawler_optimized.py** - 优化版主爬虫
   - 基于实际HTML结构的选择器
   - 多层级容错机制
   - 智能元素查找策略

2. **config.py** - 配置文件
   - 更新了基于实际结构的选择器
   - 添加了新发现的CSS类名

3. **test_optimized_crawler.py** - 测试脚本
   - 选择器有效性测试
   - 功能完整性测试

### 主要改进

#### 1. 智能元素查找
```python
def find_element_by_selectors(self, selectors, timeout=5):
    """使用多个选择器尝试查找元素"""
    for selector in selectors:
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )
            logger.debug(f"成功使用选择器找到元素: {selector}")
            return element
        except TimeoutException:
            continue
    return None
```

#### 2. 评论内容提取优化
```python
def extract_comment_content(self, comment_element):
    """提取评论内容"""
    content_selectors = [
        ".con1 .text span",      # 主要选择器
        ".text span",            # 简化路径
        ".con1 .text",           # 容器级别
        ".text",                 # 最简路径
        ".item1in .con1 .text span"  # 完整路径
    ]
    
    for selector in content_selectors:
        try:
            content_elements = comment_element.find_elements(By.CSS_SELECTOR, selector)
            if content_elements:
                # 合并所有文本内容
                content_parts = []
                for elem in content_elements:
                    text = elem.text.strip()
                    if text and text not in content_parts:
                        content_parts.append(text)
                
                content = " ".join(content_parts)
                if content:
                    return content
        except NoSuchElementException:
            continue
    return "未获取到内容"
```

#### 3. 返回机制优化
```python
def go_back_to_main_page(self):
    """返回主页面"""
    try:
        # 查找返回按钮
        back_selectors = [
            ".Bar_back_20RmD",      # 主要返回按钮
            ".woo-font--angleLeft", # 左箭头图标
            "[class*='Bar_back']",  # 模糊匹配
            ".Bar_left_2J3kl"       # 左侧区域
        ]
        
        back_button = self.find_element_by_selectors(back_selectors)
        if back_button:
            back_button.click()
            logger.info("成功点击返回按钮")
            time.sleep(3)
            return True
        else:
            # 如果找不到返回按钮，尝试浏览器后退
            self.driver.back()
            logger.info("使用浏览器后退功能")
            time.sleep(3)
            return True
    except Exception as e:
        logger.error(f"返回主页面时出错: {e}")
        return False
```

## 使用方法

### 1. 运行测试
```bash
python test_optimized_crawler.py
```

### 2. 运行爬虫
```bash
python weibo_crawler_optimized.py
```

### 3. 自定义参数
```python
# 在main()函数中修改参数
crawler.crawl_weibo_comments(
    search_url=search_url,
    max_posts=20,              # 最大微博数量
    max_comments_per_post=300  # 每个微博最大评论数
)
```

## 特点

1. **高容错性** - 多层级选择器确保在页面结构变化时仍能工作
2. **基于实际结构** - 根据真实页面HTML结构优化
3. **智能返回** - 多种返回主页面的方式
4. **详细日志** - 完整的调试和运行日志
5. **数据完整性** - 确保评论内容的完整提取

## 注意事项

1. 确保Chrome浏览器已安装
2. 网络连接稳定
3. 遵守微博的使用条款
4. 适当设置爬取间隔避免被限制

## 输出文件

- `weibo_comments.csv` - CSV格式的评论数据
- `weibo_comments.json` - JSON格式的评论数据
- `weibo_crawler.log` - 详细的运行日志
