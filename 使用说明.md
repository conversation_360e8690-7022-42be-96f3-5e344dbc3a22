# 微博用户页面评论爬虫使用说明

## 功能描述
专门用于爬取新京报用户页面中关于"重庆万州公交"事件的微博评论。

## 主要特性
- 爬取指定用户页面的微博（最多20条）
- 每条微博爬取最多300条评论
- 自动点击评论按钮进入评论页面
- 爬取完成后自动点击返回按钮回到主页面
- 支持Vue虚拟滚动页面结构
- 智能识别和排序微博（按data-index排序）
- 过滤隐藏的微博元素
- 支持数据导出为CSV和JSON格式

## 最新修复
- ✅ 修复了微博识别数量不准确的问题
- ✅ 修复了微博排序错误的问题
- ✅ 优化了Vue虚拟滚动页面的微博检测
- ✅ 添加了微博时间和索引信息显示
- ✅ 改进了隐藏微博的过滤逻辑

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行爬虫
```bash
python weibo_crawler_enhanced.py
```

### 3. 手动登录
程序启动后会自动打开浏览器，请手动登录微博账号，然后按回车键继续。

### 4. 测试微博检测（可选）
如果想测试微博识别和排序是否正常，可以运行：
```bash
python test_weibo_detection.py
```
这将显示找到的微博数量、排序信息和内容预览。

## 配置说明
主要配置在 `config.py` 文件中：
- `TARGET_URL`: 目标用户页面URL
- `WEIBO_COUNT`: 最大微博数量（20条）
- `MAX_COMMENTS_PER_WEIBO`: 每条微博最大评论数（300条）

## 输出文件
- `weibo_comments.csv`: CSV格式的评论数据
- `weibo_comments.json`: JSON格式的评论数据

## 注意事项
- 需要手动登录微博账号
- 爬取过程中请勿关闭浏览器窗口
- 建议在网络稳定的环境下运行

## 免责声明
本工具仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
