#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化版微博爬虫
基于实际HTML结构的测试脚本
"""

import time
import logging
from weibo_crawler_optimized import WeiboCommentCrawler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_crawler():
    """测试爬虫功能"""
    logger.info("开始测试优化版微博爬虫")
    
    # 创建爬虫实例
    crawler = WeiboCommentCrawler()
    
    try:
        # 初始化浏览器
        if not crawler.setup_driver():
            logger.error("浏览器初始化失败")
            return False
        
        # 测试搜索URL
        test_keyword = "人工智能"
        search_url = f"https://s.weibo.com/weibo?q={test_keyword}&page=1"
        
        logger.info(f"测试搜索URL: {search_url}")
        
        # 访问页面
        crawler.driver.get(search_url)
        time.sleep(5)
        
        # 测试查找微博卡片
        logger.info("测试查找微博卡片...")
        weibo_cards = crawler.find_elements_by_selectors([
            ".vue-recycle-scroller__item-view .wbpro-scroller-item",
            ".wbpro-scroller-item",
            "div[class*='wbpro-feed-content']"
        ])
        
        if weibo_cards:
            logger.info(f"成功找到 {len(weibo_cards)} 个微博卡片")
            
            # 测试提取第一个微博的内容
            first_weibo = weibo_cards[0]
            weibo_content = crawler.get_weibo_content(first_weibo)
            logger.info(f"第一个微博内容: {weibo_content[:100]}...")
            
            # 测试查找评论按钮
            logger.info("测试查找评论按钮...")
            comment_selectors = [
                ".toolbar_item_1ky_D .woo-font--comment",
                ".woo-font--comment",
                "span.toolbar_num_JXZuI",
                ".toolbar_iconWrap_3-r17"
            ]
            
            for selector in comment_selectors:
                try:
                    comment_button = first_weibo.find_element("css selector", selector)
                    if comment_button.is_displayed():
                        logger.info(f"找到评论按钮: {selector}")
                        break
                except:
                    continue
            else:
                logger.warning("未找到评论按钮")
            
            # 测试点击评论按钮（仅测试一个）
            logger.info("测试点击评论按钮...")
            if crawler.click_comment_button(first_weibo):
                logger.info("评论按钮点击成功")
                
                # 等待评论页面加载
                if crawler.wait_for_comment_page():
                    logger.info("评论页面加载成功")
                    
                    # 测试爬取少量评论
                    logger.info("测试爬取评论...")
                    comments = crawler.crawl_comments_in_detail_page(weibo_content, max_comments=5)
                    
                    if comments:
                        logger.info(f"成功爬取 {len(comments)} 条测试评论")
                        for i, comment in enumerate(comments[:3]):
                            logger.info(f"评论 {i+1}: {comment['评论者昵称']} - {comment['评论内容'][:50]}...")
                    else:
                        logger.warning("未能爬取到评论")
                    
                    # 测试返回主页面
                    logger.info("测试返回主页面...")
                    if crawler.go_back_to_main_page():
                        logger.info("成功返回主页面")
                    else:
                        logger.warning("返回主页面失败")
                else:
                    logger.warning("评论页面加载失败")
            else:
                logger.warning("评论按钮点击失败")
        else:
            logger.error("未找到微博卡片")
            return False
        
        logger.info("测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False
    finally:
        # 关闭浏览器
        crawler.close()

def test_selectors():
    """测试选择器有效性"""
    logger.info("开始测试选择器有效性")
    
    crawler = WeiboCommentCrawler()
    
    try:
        if not crawler.setup_driver():
            return False
        
        # 访问测试页面
        test_url = "https://s.weibo.com/weibo?q=测试&page=1"
        crawler.driver.get(test_url)
        time.sleep(5)
        
        # 测试各种选择器
        selectors_to_test = {
            "微博卡片": [
                ".vue-recycle-scroller__item-view .wbpro-scroller-item",
                ".wbpro-scroller-item",
                "div[class*='wbpro-feed-content']"
            ],
            "微博内容": [
                ".detail_wbtext_4CRf9",
                ".detail_text_1U10O .detail_wbtext_4CRf9",
                ".detail_text_1U10O .detail_ogText_22108"
            ],
            "评论按钮": [
                ".toolbar_item_1ky_D .woo-font--comment",
                ".woo-font--comment",
                "span.toolbar_num_JXZuI"
            ]
        }
        
        for selector_type, selectors in selectors_to_test.items():
            logger.info(f"测试 {selector_type} 选择器:")
            for selector in selectors:
                try:
                    elements = crawler.driver.find_elements("css selector", selector)
                    if elements:
                        logger.info(f"  ✓ {selector} - 找到 {len(elements)} 个元素")
                    else:
                        logger.info(f"  ✗ {selector} - 未找到元素")
                except Exception as e:
                    logger.info(f"  ✗ {selector} - 错误: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"选择器测试出错: {e}")
        return False
    finally:
        crawler.close()

def main():
    """主测试函数"""
    print("=" * 50)
    print("微博爬虫优化版测试")
    print("=" * 50)
    
    # 测试选择器
    print("\n1. 测试选择器有效性...")
    if test_selectors():
        print("✓ 选择器测试通过")
    else:
        print("✗ 选择器测试失败")
    
    time.sleep(2)
    
    # 测试爬虫功能
    print("\n2. 测试爬虫功能...")
    if test_crawler():
        print("✓ 爬虫功能测试通过")
    else:
        print("✗ 爬虫功能测试失败")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
